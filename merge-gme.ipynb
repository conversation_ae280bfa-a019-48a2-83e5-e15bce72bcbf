import pandas as pd
import numpy as np # 引入 numpy 以便处理 list 转换

# --- 1. 加载原始数据 (来自 JSON Lines 文件) ---
try:
    original_data_path = 'aggregated_data.jsonl'
    df_original = pd.read_json(original_data_path, lines=True)

    # 填充可能存在的空值，避免后续处理出错
    df_original['image_urls'] = df_original['image_urls'].apply(lambda x: [] if x is None or (isinstance(x, float) and pd.isna(x)) else x)
    df_original['prompt'] = df_original['prompt'].fillna('')

    print(f"从 '{original_data_path}' 加载原始数据成功。")
    print("原始 DataFrame 形状:", df_original.shape)
    print("-" * 30)

except FileNotFoundError:
    print(f"错误：找不到文件 '{original_data_path}'。请确保文件路径正确。")
    # 为了让代码可以继续执行，创建一个空的 DataFrame
    df_original = pd.DataFrame()

# --- 2. 加载 Embeddings 数据 (来自 Parquet 文件) ---
try:
    embeddings_path = 'embeddings_gme.parquet'
    df_embeddings = pd.read_parquet(embeddings_path)

    print(f"从 '{embeddings_path}' 加载 Embeddings 数据成功。")
    print("Embeddings DataFrame 形状:", df_embeddings.shape)
    print("-" * 30)

except FileNotFoundError:
    print(f"错误：找不到文件 '{embeddings_path}'。请确保文件路径正确。")
    # 为了让代码可以继续执行，创建一个空的 DataFrame
    df_embeddings = pd.DataFrame()

# --- 3. 基于 'id' 列合并两个 DataFrame ---
# 我们使用内连接 (inner join)，这样可以确保只保留那些同时存在于两个文件中的记录。
if not df_original.empty and not df_embeddings.empty:
    # 确保 'id' 列存在于两个 DataFrame 中
    if 'id' in df_original.columns and 'id' in df_embeddings.columns:
        
        # 执行合并
        merged_df = pd.merge(df_original, df_embeddings, on='id', how='inner')
        
        print("数据合并成功！")
        print("合并后的 DataFrame 形状:", merged_df.shape)
        print("合并后的列名:", merged_df.columns.tolist())
        
        # --- 4. 显示合并后的结果 ---
        print("\n合并后的数据示例:")
        
        # 创建一个方便预览的 DataFrame
        # 将 embedding 列表缩短显示，避免刷屏
        df_display = merged_df.copy()
        
        if 'last_embedding' in df_display.columns:
             df_display['last_embedding'] = df_display['last_embedding'].apply(lambda x: np.array(x[:5]).tolist() + ['...'] if isinstance(x, list) else x)
        if 'mean_embedding' in df_display.columns:
             df_display['mean_embedding'] = df_display['mean_embedding'].apply(lambda x: np.array(x[:5]).tolist() + ['...'] if isinstance(x, list) else x)
        if 'fused_embedding' in df_display.columns:
             df_display['fused_embedding'] = df_display['fused_embedding'].apply(lambda x: np.array(x[:5]).tolist() + ['...'] if isinstance(x, list) else x)

df_display.head()

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
# 确保中文字体能正常显示
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'WenQuanYi Micro Hei', 'DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False # 用来正常显示负号

def find_top_k_similar(target_id, df, embedding_col='fused_embedding', top_k=10, show_distribution=False):
    """
    根据给定的ID，查找并打印最相似的 top_k 个项目，并可选择性地显示相似度得分的整体分布。
    相似性通过【余弦相似度】计算。

    参数:
        target_id: 要查询的目标项目的ID。
        df (pd.DataFrame): 包含所有必需列的DataFrame。
        embedding_col (str): 用于计算相似性的列名 ('fused_embedding')。
        top_k (int): 要返回的最相似项目的数量。
        show_distribution (bool): 如果为True，则显示相似度得分的分布图和统计数据。
    """
    # --- 1. 验证输入和数据准备 ---
    if df.empty:
        print("错误：输入的 DataFrame 为空，无法执行查询。")
        return

    required_cols = [embedding_col, 'id', 'tv_name', 'tv_series_desc', 'tv_series_pic', 'fc_name', 'sc_name']
    if not all(col in df.columns for col in required_cols):
        print(f"错误: DataFrame 中缺少必要的列。需要: {required_cols}, 实际有: {df.columns.tolist()}")
        return

    target_row = df[df['id'] == target_id]
    if target_row.empty:
        print(f"错误: 无法在 DataFrame 中找到 ID 为 '{target_id}' 的项目。")
        return

    target_vector = np.array(target_row[embedding_col].iloc[0], dtype=np.float32)
    all_vectors = np.stack(df[embedding_col].apply(lambda x: np.array(x, dtype=np.float32)).values)

    # --- 2. 计算余弦相似度 ---
    norm_target = np.linalg.norm(target_vector)
    norm_all = np.linalg.norm(all_vectors, axis=1)
    dot_product = all_vectors.dot(target_vector)
    similarity_scores = dot_product / ((norm_all * norm_target) + 1e-8)
    
    # 将分数添加到临时DataFrame中以进行后续操作
    results_df = df[required_cols].copy()
    results_df['cosine_similarity'] = similarity_scores

    # --- 3. (可选) 显示相似度分布 ---
    if show_distribution:
        scores_series = pd.Series(similarity_scores)
        print("\n" + "="*20 + " 全体余弦相似度分布统计 " + "="*20)
        print(scores_series.describe())
        print("="*66)
        
        plt.figure(figsize=(12, 6))
        sns.histplot(scores_series, bins=50, kde=True, color='skyblue')
        
        # 为了标记，需要先对结果排序
        sorted_results = results_df.sort_values(by='cosine_similarity', ascending=False)
        sorted_results = sorted_results[sorted_results['id'] != target_id]
        min_score_in_top_k = sorted_results.head(top_k)['cosine_similarity'].min()
        
        plt.axvline(min_score_in_top_k, color='red', linestyle='--', label=f'Top-{top_k} 最低分: {min_score_in_top_k:.4f}')
        plt.title(f"与《{target_row['tv_name'].iloc[0]}》的余弦相似度分布图", fontsize=16)
        plt.xlabel("余弦相似度", fontsize=12)
        plt.ylabel("频数", fontsize=12)
        plt.legend()
        plt.grid(axis='y', alpha=0.75)
        plt.show()

    # --- 4. 排序并筛选结果 ---
    results_df = results_df.sort_values(by='cosine_similarity', ascending=False)
    results_df = results_df[results_df['id'] != target_id]
    top_results = results_df.head(top_k)

    # --- 5. 打印结果 ---
    print("\n" + "="*30 + " 查询目标 " + "="*30)
    print(f"剧名: 《{target_row['tv_name'].iloc[0]}》 (ID: {target_id})")
    
    # **新增点**: 打印目标剧集的简介
    target_desc = target_row['tv_series_desc'].iloc[0]
    desc_to_print = target_desc if pd.notna(target_desc) else "无"
    print(f"简介: {desc_to_print}")
    
    fc_name = target_row['fc_name'].iloc[0]
    sc_name = target_row['sc_name'].iloc[0]
    print(f"分类: {fc_name if pd.notna(fc_name) else 'N/A'} / {sc_name if pd.notna(sc_name) else 'N/A'}")
    print("="*70)

    # print(f"\n--- 基于 '{embedding_col}' 余弦相似度的 Top {top_k} 相似剧集 ---")
    if top_results.empty:
        print("未找到其他相似的剧集。")
    else:
        for i, (_, row) in enumerate(top_results.iterrows(), 1):
            print(f"\n--- Top {i} ---")
            print(f"剧名: 《{row['tv_name']}》")
            print(f"余弦相似度: {row['cosine_similarity']:.4f}")
            fc = row['fc_name'] if pd.notna(row['fc_name']) else "N/A"
            sc = row['sc_name'] if pd.notna(row['sc_name']) else "N/A"
            print(f"分类: {fc} / {sc}")
            desc = row['tv_series_desc'] if pd.notna(row['tv_series_desc']) else "无"
            print(f"简介: {desc[:100] + '...' if len(desc) > 100 else desc}")
            
    print("\n" + "="*70)
# last_embedding, mean_embedding
target_id=merged_df.loc[merged_df['tv_name'] == '爱你不是三两天', 'id'].values[0]
find_top_k_similar(target_id=target_id, df=merged_df, top_k=10,show_distribution=True)
# find_top_k_similar(target_id=9643770, df=merged_df, embedding_col='last_embedding',top_k=20,show_distribution=True)

import pandas as pd

# --- 1. 保存最终的 DataFrame 到 Parquet 文件 ---

# 假设 merged_df 已经存在于您的环境中
if 'merged_df' in locals() and not merged_df.empty:
    
    # 定义需要保存的列
    columns_to_save = [
        'id', 
        'tv_name', 
        'tv_series_desc', 
        'fc_name', 
        'sc_name', 
        'cluster_desc',
        'fused_embedding'  # 注意：这里我们只保存了 mean_embedding
    ]
    
    # 检查所有需要的列是否存在
    if all(col in merged_df.columns for col in columns_to_save):
        
        # 从 merged_df 中筛选出这些列
        final_df = merged_df[columns_to_save]
        if 'fused_embedding' in final_df.columns:
            final_df = final_df.rename(columns={'fused_embedding': 'mean_embedding'})

        # 定义输出文件名
        output_file = 'final_data_with_embeddings.parquet'
        
        # 保存到 Parquet 文件
        final_df.to_parquet(output_file, index=False)
        
        print(f"成功将指定的 {len(columns_to_save)} 列数据保存到文件: '{output_file}'")
        print("保存的数据形状:", final_df.shape)
        print("保存的数据列名:", final_df.columns.tolist())
        print("\n保存数据的前5行预览:")
        print(final_df.head())
        
    else:
        # 如果有列缺失，打印错误信息
        missing_cols = [col for col in columns_to_save if col not in merged_df.columns]
        print(f"错误：'merged_df' 中缺少以下必需列，无法保存: {missing_cols}")

else:
    print("错误：未找到名为 'merged_df' 的 DataFrame 或其为空，无法执行保存操作。")


merged_df.shape

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.metrics.pairwise import cosine_similarity
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'WenQuanYi Micro Hei', 'DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

# 假设merged_df是您现有的数据
print("数据形状:", merged_df.shape)
print("可用列:", merged_df.columns.tolist())

# 准备向量数据
embedding_col = 'fused_embedding'  # 可以选择不同的embedding
vectors = np.array(merged_df[embedding_col].tolist())
print(f"向量形状: {vectors.shape}")

# 标准化向量
scaler = StandardScaler()
vectors_scaled = scaler.fit_transform(vectors)

# 降维方法1: PCA
pca = PCA(n_components=2, random_state=42)
vectors_pca = pca.fit_transform(vectors_scaled)
print(f"PCA解释方差比: {pca.explained_variance_ratio_}")

# 降维方法2: t-SNE
tsne = TSNE(n_components=2, random_state=42, perplexity=30, max_iter=1000)
vectors_tsne = tsne.fit_transform(vectors_scaled)

# 降维方法3: UMAP (正确的导入方式)
try:
    import umap.umap_ as umap
    umap_reducer = umap.UMAP(n_components=2, random_state=42, n_neighbors=15, min_dist=0.1)
    vectors_umap = umap_reducer.fit_transform(vectors_scaled)
    umap_available = True
    print("UMAP降维完成")
except ImportError:
    print("UMAP未安装，跳过UMAP降维")
    vectors_umap = vectors_pca  # 使用PCA作为替代
    umap_available = False
except Exception as e:
    print(f"UMAP运行出错: {e}")
    vectors_umap = vectors_pca  # 使用PCA作为替代
    umap_available = False

# 创建降维后的DataFrame
df_reduced = merged_df[['id', 'tv_name', 'tv_series_desc', 'fc_name', 'sc_name']].copy()
df_reduced['pca_x'] = vectors_pca[:, 0]
df_reduced['pca_y'] = vectors_pca[:, 1]
df_reduced['tsne_x'] = vectors_tsne[:, 0]
df_reduced['tsne_y'] = vectors_tsne[:, 1]

if umap_available:
    df_reduced['umap_x'] = vectors_umap[:, 0]
    df_reduced['umap_y'] = vectors_umap[:, 1]

print("降维完成！")
print(f"PCA降维结果形状: {vectors_pca.shape}")
print(f"t-SNE降维结果形状: {vectors_tsne.shape}")
if umap_available:
    print(f"UMAP降维结果形状: {vectors_umap.shape}")

# 使用UMAP降维后的数据进行聚类
X = vectors_umap

# 方法1: K-means聚类
def kmeans_clustering(X, max_k=10):
    """K-means聚类，自动选择最优K值"""
    inertias = []
    silhouette_scores = []
    k_range = range(2, max_k + 1)
    
    for k in k_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        kmeans.fit(X)
        inertias.append(kmeans.inertia_)
        silhouette_scores.append(silhouette_score(X, kmeans.labels_))
    
    # 肘部法则
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(k_range, inertias, 'bo-')
    plt.xlabel('聚类数 (K)')
    plt.ylabel('惯性 (Inertia)')
    plt.title('肘部法则 - 选择最优K值')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(k_range, silhouette_scores, 'ro-')
    plt.xlabel('聚类数 (K)')
    plt.ylabel('轮廓系数 (Silhouette Score)')
    plt.title('轮廓系数 - 选择最优K值')
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    # 选择最优K值（轮廓系数最高）
    optimal_k = k_range[np.argmax(silhouette_scores)]
    print(f"最优聚类数: {optimal_k}")
    
    # 使用最优K值进行聚类
    optimal_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
    kmeans_labels = optimal_kmeans.fit_predict(X)
    
    return kmeans_labels, optimal_k, inertias, silhouette_scores

# 执行K-means聚类
kmeans_labels, optimal_k, inertias, silhouette_scores = kmeans_clustering(X)

# 方法2: DBSCAN聚类
def dbscan_clustering(X):
    """DBSCAN聚类，自动选择参数"""
    # 计算距离矩阵的统计信息来估计eps
    from sklearn.neighbors import NearestNeighbors
    nbrs = NearestNeighbors(n_neighbors=5).fit(X)
    distances, indices = nbrs.kneighbors(X)
    distances = np.sort(distances[:, 4])  # 第5个最近邻的距离
    
    # 绘制距离图来选择eps
    plt.figure(figsize=(10, 6))
    plt.plot(distances)
    plt.xlabel('样本索引')
    plt.ylabel('第5个最近邻距离')
    plt.title('DBSCAN eps参数选择')
    plt.grid(True)
    plt.show()
    
    # 选择eps（距离曲线的拐点）
    eps = np.percentile(distances, 90)  # 使用90%分位数作为eps
    print(f"选择的eps值: {eps:.4f}")
    
    dbscan = DBSCAN(eps=eps, min_samples=5)
    dbscan_labels = dbscan.fit_predict(X)
    
    n_clusters = len(set(dbscan_labels)) - (1 if -1 in dbscan_labels else 0)
    n_noise = list(dbscan_labels).count(-1)
    
    print(f"DBSCAN聚类结果: {n_clusters}个聚类, {n_noise}个噪声点")
    
    return dbscan_labels, eps

# 执行DBSCAN聚类
dbscan_labels, eps = dbscan_clustering(X)

# 方法3: 层次聚类
def hierarchical_clustering(X, n_clusters=5):
    """层次聚类"""
    hierarchical = AgglomerativeClustering(n_clusters=n_clusters)
    hierarchical_labels = hierarchical.fit_predict(X)
    
    return hierarchical_labels

# 执行层次聚类
hierarchical_labels = hierarchical_clustering(X, n_clusters=optimal_k)

# 将聚类结果添加到DataFrame
df_reduced['kmeans_cluster'] = kmeans_labels
df_reduced['dbscan_cluster'] = dbscan_labels
df_reduced['hierarchical_cluster'] = hierarchical_labels

print("聚类完成！")

# 可视化函数
def plot_clusters(X, labels, title, method_name):
    """绘制聚类结果"""
    plt.figure(figsize=(12, 8))
    
    # 获取唯一标签
    unique_labels = np.unique(labels)
    colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))
    
    for i, label in enumerate(unique_labels):
        if label == -1:  # 噪声点
            mask = labels == label
            plt.scatter(X[mask, 0], X[mask, 1], c='black', marker='x', s=50, alpha=0.6, label='噪声')
        else:
            mask = labels == label
            plt.scatter(X[mask, 0], X[mask, 1], c=colors[i], s=50, alpha=0.7, label=f'聚类 {label}')
    
    plt.title(f'{method_name} - {title}', fontsize=16)
    plt.xlabel('PCA Component 1', fontsize=12)
    plt.ylabel('PCA Component 2', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

# 绘制不同方法的聚类结果
plot_clusters(vectors_pca, kmeans_labels, 'K-means聚类结果', 'K-means')
plot_clusters(vectors_pca, dbscan_labels, 'DBSCAN聚类结果', 'DBSCAN')
plot_clusters(vectors_pca, hierarchical_labels, '层次聚类结果', '层次聚类')

# 比较不同降维方法的聚类效果
def compare_dimension_reduction_methods():
    """比较不同降维方法的聚类效果"""
    methods = {
        'PCA': vectors_pca,
        't-SNE': vectors_tsne,
        'UMAP': vectors_umap
    }
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    for i, (method_name, vectors_2d) in enumerate(methods.items()):
        # 使用K-means聚类
        kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
        labels = kmeans.fit_predict(vectors_2d)
        
        # 计算轮廓系数
        silhouette_avg = silhouette_score(vectors_2d, labels)
        
        # 绘制结果
        unique_labels = np.unique(labels)
        colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))
        
        for j, label in enumerate(unique_labels):
            mask = labels == label
            axes[i].scatter(vectors_2d[mask, 0], vectors_2d[mask, 1], 
                          c=colors[j], s=30, alpha=0.7, label=f'聚类 {label}')
        
        axes[i].set_title(f'{method_name}\n轮廓系数: {silhouette_avg:.3f}', fontsize=14)
        axes[i].set_xlabel(f'{method_name} Component 1')
        axes[i].set_ylabel(f'{method_name} Component 2')
        axes[i].legend()
        axes[i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# 执行比较
compare_dimension_reduction_methods()

# 分析聚类结果
def analyze_clusters(df, cluster_col='kmeans_cluster'):
    """分析聚类结果，显示每个聚类的特征"""
    print(f"=== {cluster_col} 聚类分析 ===")
    
    # 基本统计
    cluster_counts = df[cluster_col].value_counts().sort_index()
    print(f"\n聚类分布:")
    for cluster_id, count in cluster_counts.items():
        print(f"聚类 {cluster_id}: {count} 个样本 ({count/len(df)*100:.1f}%)")
    
    # 分析每个聚类的特征
    for cluster_id in sorted(df[cluster_col].unique()):
        if cluster_id == -1:  # 跳过噪声点
            continue
            
        cluster_data = df[df[cluster_col] == cluster_id]
        print(f"\n--- 聚类 {cluster_id} 分析 ---")
        print(f"样本数量: {len(cluster_data)}")
        
        # 分类分布
        if 'fc_name' in cluster_data.columns:
            fc_dist = cluster_data['fc_name'].value_counts().head(3)
            print(f"主要分类: {fc_dist.to_dict()}")
        
        if 'sc_name' in cluster_data.columns:
            sc_dist = cluster_data['sc_name'].value_counts().head(3)
            print(f"子分类: {sc_dist.to_dict()}")
        
        # 显示代表性样本
        print("代表性样本:")
        for i, (_, row) in enumerate(cluster_data.head(3).iterrows()):
            print(f"  {i+1}. {row['tv_name']}")

# 分析K-means聚类结果
analyze_clusters(df_reduced, 'kmeans_cluster')

# 分析DBSCAN聚类结果
analyze_clusters(df_reduced, 'dbscan_cluster')

# 创建交互式散点图（需要plotly）
try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    
    # 创建交互式聚类图
    fig = px.scatter(
        df_reduced, 
        x='pca_x', 
        y='pca_y',
        color='kmeans_cluster',
        hover_data=['tv_name', 'fc_name', 'sc_name'],
        title='视频内容聚类分析 - 交互式视图',
        labels={'pca_x': 'PCA Component 1', 'pca_y': 'PCA Component 2'}
    )
    
    fig.update_layout(
        width=1000,
        height=600,
        showlegend=True
    )
    
    fig.show()
    
    # 创建3D散点图
    if 'fused_embedding' in merged_df.columns:
        # 使用PCA降维到3D
        pca_3d = PCA(n_components=3, random_state=42)
        vectors_3d = pca_3d.fit_transform(vectors_scaled)
        
        df_3d = df_reduced.copy()
        df_3d['pca_x_3d'] = vectors_3d[:, 0]
        df_3d['pca_y_3d'] = vectors_3d[:, 1]
        df_3d['pca_z_3d'] = vectors_3d[:, 2]
        
        fig_3d = px.scatter_3d(
            df_3d,
            x='pca_x_3d',
            y='pca_y_3d', 
            z='pca_z_3d',
            color='kmeans_cluster',
            hover_data=['tv_name', 'fc_name'],
            title='视频内容聚类分析 - 3D视图'
        )
        
        fig_3d.show()
        
except ImportError:
    print("plotly未安装，跳过交互式可视化")

# 聚类质量评估
def evaluate_clustering_quality(X, labels_dict):
    """评估不同聚类方法的质量"""
    results = {}
    
    for method_name, labels in labels_dict.items():
        if len(np.unique(labels)) > 1:  # 至少需要2个聚类
            silhouette_avg = silhouette_score(X, labels)
            calinski_avg = calinski_harabasz_score(X, labels)
            
            results[method_name] = {
                'silhouette_score': silhouette_avg,
                'calinski_harabasz_score': calinski_avg,
                'n_clusters': len(np.unique(labels))
            }
    
    # 创建评估结果表格
    eval_df = pd.DataFrame(results).T
    print("聚类质量评估结果:")
    print(eval_df.round(4))
    
    # 可视化评估结果
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # 轮廓系数比较
    methods = list(results.keys())
    silhouette_scores = [results[m]['silhouette_score'] for m in methods]
    
    axes[0].bar(methods, silhouette_scores, color='skyblue', alpha=0.7)
    axes[0].set_title('轮廓系数比较')
    axes[0].set_ylabel('轮廓系数')
    axes[0].grid(True, alpha=0.3)
    
    # Calinski-Harabasz指数比较
    calinski_scores = [results[m]['calinski_harabasz_score'] for m in methods]
    
    axes[1].bar(methods, calinski_scores, color='lightcoral', alpha=0.7)
    axes[1].set_title('Calinski-Harabasz指数比较')
    axes[1].set_ylabel('Calinski-Harabasz指数')
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return results

# 评估所有聚类方法
labels_dict = {
    'K-means': kmeans_labels,
    'DBSCAN': dbscan_labels,
    'Hierarchical': hierarchical_labels
}

evaluation_results = evaluate_clustering_quality(vectors_pca, labels_dict)

